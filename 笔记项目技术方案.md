# 笔记项目技术方案

## 项目概述
基于 React 19 开发的纯前端知识管理系统varus，支持知识库、分组、文档管理，具备知识网络可视化、双向链接、划词评论等高级功能。

## 核心功能需求分析

### 1. 知识库管理
- 创建、删除、重命名知识库
- 知识库权限控制（本地存储）
- 知识库导入导出

### 2. 分组与文档管理  
- 支持无限层级嵌套的树形分组结构
- 分组可包含子分组和文档，支持混合嵌套
- 拖拽排序和移动（分组间、分组内、跨层级）
- 右键菜单快速创建分组和文档
- 文档的CRUD操作
- 所见即所得的Markdown编辑器作为默认编辑模式
- 源码编辑和预览模式放在设置选项中

### 3. 知识网络
- 文档间的引用关系建立
- 图形化展示知识网络
- 动画效果展示引用流动
- 双向链接导航

### 4. 交互功能
- 划词评论系统（支持右键菜单快捷操作）
- 主编辑区工具栏（撤销重做、字体大小、颜色、标题样式、加粗等）
- 全文搜索
- 标签系统
- 历史版本管理

## 技术架构设计

### 整体架构
```
┌─────────────────────────────────┐
│           前端应用层              │
├─────────────────────────────────┤
│         React 19 组件树          │
├─────────────────────────────────┤
│        状态管理 (Zustand)        │
├─────────────────────────────────┤
│         数据持久化层              │
├─────────────────────────────────┤
│        本地存储 (IndexedDB)       │
└─────────────────────────────────┘
```

### 技术栈选型

#### 核心框架
- **React 19**: 最新特性，提供更好的并发渲染和服务器组件支持
- **TypeScript**: 类型安全，提高开发效率
- **Vite**: 快速构建工具

#### 状态管理
- **Zustand**: 轻量级状态管理，替代 Redux
- **Immer**: 不可变数据更新

#### UI 组件库
- **Tailwind CSS**: 原子化CSS框架
- **Headless UI**: 无样式组件库
- **Lucide React**: 图标库

#### 数据存储
- **Dexie.js**: IndexedDB 包装器，用于本地数据持久化
- **localforage**: 统一的本地存储API

#### 编辑器相关
- **@lexical/react**: Meta开发的现代富文本编辑器框架，支持所见即所得
- **@lexical/markdown**: Lexical的Markdown插件，支持Markdown语法
- **@lexical/rich-text**: 富文本编辑功能
- **@lexical/history**: 撤销重做功能
- **@monaco-editor/react**: 代码编辑器（设置中的源码模式）
- **react-markdown**: Markdown 渲染（设置中的预览模式）
- **remark/rehype**: Markdown 处理插件生态

#### 图形可视化
- **D3.js**: 数据可视化，用于知识网络图
- **react-force-graph**: 力导向图组件
- **framer-motion**: 动画库

#### 搜索功能
- **Fuse.js**: 模糊搜索库
- **mark.js**: 关键词高亮

## 数据结构设计

### 1. 知识库 (KnowledgeBase)
```typescript
interface KnowledgeBase {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  settings: {
    theme: string;
    defaultView: string;
  };
}
```

### 2. 分组 (Group)
```typescript
interface Group {
  id: string;
  knowledgeBaseId: string;
  name: string;
  parentId?: string;  // 支持嵌套，null表示根分组
  path: string;       // 分组路径，如 "root/group1/subgroup1"
  level: number;      // 嵌套层级，根分组为0
  order: number;      // 同层级内的排序
  type: 'folder';     // 分组类型标识
  children?: (Group | Document)[];  // 子分组和文档
  collapsed?: boolean; // 是否折叠显示
  createdAt: Date;
  updatedAt: Date;
}
```

### 3. 文档 (Document)
```typescript
interface Document {
  id: string;
  knowledgeBaseId: string;
  groupId?: string;
  title: string;
  content: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  version: number;
  references: string[];    // 引用的文档ID
  backlinks: string[];     // 被引用的文档ID
}
```

### 4. 链接关系 (Link)
```typescript
interface Link {
  id: string;
  sourceDocId: string;
  targetDocId: string;
  sourceText: string;      // 引用的文本片段
  targetText?: string;     // 被引用的文本片段
  type: 'reference' | 'mention' | 'tag';
  createdAt: Date;
}
```

### 5. 评论 (Comment)
```typescript
interface Comment {
  id: string;
  documentId: string;
  selectedText: string;
  content: string;
  position: {
    start: number;
    end: number;
  };
  contextMenu: {
    x: number;        // 右键菜单位置
    y: number;
  };
  reactions?: string[];  // 表情回应
  createdAt: Date;
  resolved: boolean;
}
```

### 6. 右键菜单配置 (ContextMenu)
```typescript
interface ContextMenuConfig {
  selection: {
    enabled: boolean;
    actions: ('comment' | 'highlight' | 'link' | 'copy')[];
  };
  sidebar: {
    enabled: boolean;
    actions: ('newGroup' | 'newDocument' | 'rename' | 'delete' | 'move')[];
  };
  editor: {
    enabled: boolean;
    actions: ('format' | 'insert' | 'reference')[];
  };
}
```

## 核心功能模块设计

### 1. 数据管理模块 (DataManager)
- 使用 Dexie.js 管理 IndexedDB
- 提供 CRUD 操作接口
- 数据版本控制和迁移

### 2. 编辑器模块 (Editor)
- Lexical 富文本编辑器集成（默认模式）
- 所见即所得的 Markdown 编辑体验
- 工具栏功能：撤销重做、字体样式、颜色、标题层级、格式化等
- Monaco Editor 集成（源码编辑模式，在设置中可选）
- Markdown 预览模式（在设置中可选）
- 语法高亮和自动补全
- 双向链接语法解析 `[[文档名]]`
- 右键菜单集成（格式化、插入、引用等快捷操作）

### 3. 知识网络模块 (KnowledgeGraph)
- D3.js 力导向图可视化
- 节点和边的交互
- 动态布局算法
- 缩放和拖拽支持

### 4. 搜索模块 (SearchEngine)
- 全文索引构建
- 实时搜索建议
- 高级搜索过滤
- 搜索结果高亮

### 5. 评论系统 (CommentSystem)
- 文本选中检测
- 右键菜单快速评论入口
- 评论气泡UI和浮动面板
- 评论线程管理
- 已解决状态跟踪
- 表情回应功能

## UI/UX 设计规划

### 页面布局结构
```
┌─────────────────────────────────────────────────┐
│                  顶部导航栏                        │
├──────────┬─────────────────────┬─────────────────┤
│          │   ┌─────────────┐   │                 │
│  侧边栏   │   │  工具栏     │   │   右侧面板       │
│          │   └─────────────┘   │                 │
│ 知识库   │   富文本编辑器       │  大纲/引用      │
│ 分组树   │   (所见即所得)      │  评论列表       │
│(右键菜单)│                     │                 │
└──────────┴─────────────────────┴─────────────────┘
```

### 主要界面设计

#### 1. 知识库首页
- 卡片式知识库展示
- 快速创建入口
- 最近访问记录

#### 2. 编辑界面
- 分栏布局：侧边栏 + 工具栏 + 富文本编辑区 + 右侧面板
- 可折叠的侧边栏（支持右键菜单创建分组/文档）
- 主编辑区工具栏：撤销/重做、字体大小、颜色选择器、标题样式、加粗/斜体等
- 标签页式文档切换
- 右键菜单支持：文本选择后的评论、高亮、创建链接等操作

#### 3. 知识网络视图
- 全屏图形界面
- 节点详情悬浮层
- 关系筛选控件

#### 4. 搜索界面
- 实时搜索建议
- 搜索结果分类展示
- 关键词高亮

## 开发路线图

### 第一阶段：基础框架 (2-3周)
1. 项目初始化，技术栈搭建
2. 基础路由和页面结构
3. 数据层设计和 IndexedDB 集成
4. 基础 UI 组件开发

### 第二阶段：核心功能 (3-4周)
1. 知识库和嵌套分组管理
2. 文档 CRUD 操作
3. Lexical 富文本编辑器集成（所见即所得）
4. 编辑器工具栏开发
5. 右键菜单系统开发
6. 基础搜索功能

### 第三阶段：高级功能 (4-5周)
1. 双向链接系统
2. 知识网络可视化
3. 划词评论功能
4. 标签系统

### 第四阶段：优化与完善 (2-3周)
1. 性能优化
2. 用户体验优化
3. 数据导入导出
4. 主题和个性化设置

## 关键技术实现点

### 0. 工具栏组件实现
```typescript
// 编辑器工具栏配置
interface ToolbarConfig {
  groups: {
    history: {
      undo: boolean;
      redo: boolean;
    };
    formatting: {
      bold: boolean;
      italic: boolean;
      underline: boolean;
      strikethrough: boolean;
    };
    typography: {
      fontSize: boolean;
      fontColor: boolean;
      backgroundColor: boolean;
      fontFamily: boolean;
    };
    structure: {
      headings: boolean;    // H1-H6
      lists: boolean;       // 有序/无序列表
      blockquote: boolean;  // 引用块
      codeBlock: boolean;   // 代码块
    };
    insert: {
      link: boolean;
      image: boolean;
      table: boolean;
      divider: boolean;
    };
  };
}

// 工具栏组件
const EditorToolbar: React.FC<{
  editor: LexicalEditor;
  config: ToolbarConfig;
}> = ({ editor, config }) => {
  return (
    <div className="flex items-center gap-2 p-2 border-b bg-white">
      {config.groups.history.undo && (
        <button onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}>
          撤销
        </button>
      )}
      {config.groups.history.redo && (
        <button onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}>
          重做
        </button>
      )}
      
      <div className="w-px h-6 bg-gray-300 mx-2" />
      
      {config.groups.formatting.bold && (
        <button onClick={() => editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold')}>
          加粗
        </button>
      )}
      
      {config.groups.typography.fontSize && (
        <select onChange={(e) => {
          // 字体大小逻辑
        }}>
          <option value="12">12px</option>
          <option value="14">14px</option>
          <option value="16">16px</option>
          <option value="18">18px</option>
        </select>
      )}
      
      {config.groups.typography.fontColor && (
        <input 
          type="color" 
          onChange={(e) => {
            // 字体颜色逻辑
          }}
        />
      )}
    </div>
  );
};
```

### 1. 双向链接实现
```typescript
// 链接语法解析
const parseLinkSyntax = (content: string) => {
  const linkRegex = /\[\[([^\]]+)\]\]/g;
  const links = [];
  let match;
  
  while ((match = linkRegex.exec(content)) !== null) {
    links.push({
      text: match[1],
      start: match.index,
      end: match.index + match[0].length
    });
  }
  
  return links;
};
```

### 2. 知识网络数据结构
```typescript
// 图数据格式
const graphData = {
  nodes: documents.map(doc => ({
    id: doc.id,
    name: doc.title,
    group: doc.groupId,
    size: doc.references.length + doc.backlinks.length
  })),
  links: links.map(link => ({
    source: link.sourceDocId,
    target: link.targetDocId,
    value: 1
  }))
};
```

### 3. 右键菜单系统实现
```typescript
// 右键菜单处理
const useContextMenu = () => {
  const [menuState, setMenuState] = useState<{
    visible: boolean;
    x: number;
    y: number;
    type: 'selection' | 'sidebar' | 'editor';
    selectedText?: string;
  }>({
    visible: false,
    x: 0,
    y: 0,
    type: 'editor'
  });

  const handleContextMenu = useCallback((e: MouseEvent) => {
    e.preventDefault();
    const selection = window.getSelection();
    const hasSelection = selection && selection.toString().trim();
    
    setMenuState({
      visible: true,
      x: e.clientX,
      y: e.clientY,
      type: hasSelection ? 'selection' : 'editor',
      selectedText: hasSelection ? selection.toString() : undefined
    });
  }, []);

  const hideMenu = useCallback(() => {
    setMenuState(prev => ({ ...prev, visible: false }));
  }, []);

  return { menuState, handleContextMenu, hideMenu };
};

// 右键菜单组件
const ContextMenu: React.FC<{
  state: MenuState;
  onAction: (action: string) => void;
}> = ({ state, onAction }) => {
  if (!state.visible) return null;

  const getMenuItems = () => {
    switch (state.type) {
      case 'selection':
        return [
          { key: 'comment', label: '添加评论', icon: '💬' },
          { key: 'highlight', label: '高亮文本', icon: '🖍️' },
          { key: 'link', label: '创建链接', icon: '🔗' },
          { key: 'copy', label: '复制', icon: '📋' }
        ];
      case 'sidebar':
        return [
          { key: 'newGroup', label: '新建分组', icon: '📁' },
          { key: 'newDocument', label: '新建文档', icon: '📄' },
          { key: 'rename', label: '重命名', icon: '✏️' },
          { key: 'delete', label: '删除', icon: '🗑️' }
        ];
      default:
        return [];
    }
  };

  return (
    <div 
      className="fixed bg-white shadow-lg border rounded-md py-2 z-50"
      style={{ left: state.x, top: state.y }}
    >
      {getMenuItems().map(item => (
        <button
          key={item.key}
          className="w-full px-4 py-2 text-left hover:bg-gray-100"
          onClick={() => onAction(item.key)}
        >
          {item.icon} {item.label}
        </button>
      ))}
    </div>
  );
};
```

### 4. 嵌套分组组件实现
```typescript
// 递归树形组件
const NestedGroupTree: React.FC<{
  groups: Group[];
  documents: Document[];
  level?: number;
  onContextMenu: (e: MouseEvent, type: 'group' | 'document', item: Group | Document) => void;
}> = ({ groups, documents, level = 0, onContextMenu }) => {
  const [collapsed, setCollapsed] = useState<Record<string, boolean>>({});

  const toggleCollapse = (groupId: string) => {
    setCollapsed(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };

  const renderGroupItem = (group: Group) => (
    <div key={group.id} className={`ml-${level * 4}`}>
      <div 
        className="flex items-center py-1 px-2 hover:bg-gray-100 cursor-pointer"
        onContextMenu={(e) => onContextMenu(e.nativeEvent, 'group', group)}
      >
        <button 
          onClick={() => toggleCollapse(group.id)}
          className="mr-2"
        >
          {collapsed[group.id] ? '▶' : '▼'}
        </button>
        <span>📁 {group.name}</span>
      </div>
      
      {!collapsed[group.id] && (
        <div className="ml-4">
          <NestedGroupTree
            groups={groups.filter(g => g.parentId === group.id)}
            documents={documents.filter(d => d.groupId === group.id)}
            level={level + 1}
            onContextMenu={onContextMenu}
          />
        </div>
      )}
    </div>
  );

  const renderDocumentItem = (document: Document) => (
    <div 
      key={document.id}
      className={`ml-${level * 4 + 4} flex items-center py-1 px-2 hover:bg-gray-100 cursor-pointer`}
      onContextMenu={(e) => onContextMenu(e.nativeEvent, 'document', document)}
    >
      <span>📄 {document.title}</span>
    </div>
  );

  return (
    <div>
      {groups.filter(g => !g.parentId || (level === 0 && g.level === level)).map(renderGroupItem)}
      {documents.filter(d => !d.groupId || (level === 0 && !d.groupId)).map(renderDocumentItem)}
    </div>
  );
};
```

## 性能优化策略

1. **虚拟化列表**: 大量文档时使用虚拟滚动
2. **懒加载**: 按需加载文档内容
3. **缓存策略**: 智能缓存编辑状态
4. **Web Workers**: 后台处理搜索索引
5. **代码分割**: 路由级别的代码分割

## 数据安全考虑

1. **本地加密**: 敏感数据本地加密存储
2. **版本控制**: 文档历史版本管理
3. **数据备份**: 支持数据导出备份
4. **增量同步**: 预留云端同步接口

## 组件自主完成策略

### 核心自研组件列表
1. **富文本编辑器工具栏**: 基于 Lexical 自主开发，避免依赖第三方工具栏组件
2. **嵌套树形导航**: 自主实现递归树形结构，支持无限层级嵌套
3. **右键菜单系统**: 自主开发上下文菜单，支持多场景适配
4. **知识网络图**: 基于 D3.js 自主开发力导向图组件
5. **评论系统**: 自主开发划词评论和浮动面板
6. **搜索组件**: 自主实现搜索建议和结果高亮
7. **拖拽排序**: 自主开发树形结构的拖拽功能

### 最小化第三方依赖原则
1. **UI组件**: 仅使用 Headless UI 作为无样式基础，所有样式自主完成
2. **图标**: 使用 Lucide React，但考虑后期替换为自主图标库
3. **动画**: 使用 Framer Motion，但仅用于复杂动画，简单过渡使用 CSS
4. **编辑器**: 基于 Lexical 核心，所有插件和扩展自主开发

### 组件开发规范
```typescript
// 组件结构标准
interface ComponentProps {
  // 必需属性
  id: string;
  className?: string;
  
  // 事件处理
  onClick?: (event: MouseEvent) => void;
  onContextMenu?: (event: MouseEvent) => void;
  
  // 自定义配置
  config?: ComponentConfig;
  
  // 主题支持
  theme?: 'light' | 'dark' | 'auto';
}

// 组件配置标准
interface ComponentConfig {
  enabled: boolean;
  position?: 'top' | 'bottom' | 'left' | 'right';
  animation?: boolean;
  shortcuts?: Record<string, string>;
}

// 组件样式管理
const useComponentStyles = (theme: string, config: ComponentConfig) => {
  return useMemo(() => ({
    container: `component-base ${theme}-theme`,
    // 基于配置生成样式类
  }), [theme, config]);
};
```

## 扩展性设计

1. **插件系统**: 预留插件接口，支持组件扩展
2. **主题系统**: 可切换的UI主题，所有自研组件支持主题
3. **导出格式**: 多种格式导出支持
4. **API接口**: 预留外部集成API
5. **组件库**: 将自研组件抽取为独立组件库，便于复用
